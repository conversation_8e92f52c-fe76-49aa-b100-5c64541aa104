#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/wait.h>
#include <sys/stat.h>
#include <errno.h>

// 全局变量：存储环境变量
extern char **environ;

/**
 * 在PATH环境变量中搜索可执行文件
 * @param command 要搜索的命令名
 * @return 找到的完整路径，需要调用者释放内存；如果未找到返回NULL
 */
char* find_executable_in_path(const char* command) {
    // 如果命令以'/'开头，说明是绝对路径，直接检查是否可执行
    if (command[0] == '/') {
        struct stat st;
        if (stat(command, &st) == 0 && (st.st_mode & S_IXUSR)) {
            return strdup(command);
        }
        return NULL;
    }

    // 获取PATH环境变量
    char* path_env = getenv("PATH");
    if (!path_env) {
        fprintf(stderr, "PATH环境变量未设置\n");
        return NULL;
    }

    // 复制PATH环境变量，因为strtok会修改原字符串
    char* path_copy = strdup(path_env);
    if (!path_copy) {
        perror("内存分配失败");
        return NULL;
    }

    char* dir = strtok(path_copy, ":");
    char* full_path = NULL;

    // 遍历PATH中的每个目录
    while (dir != NULL) {
        // 构造完整路径
        size_t path_len = strlen(dir) + strlen(command) + 2; // +2 for '/' and '\0'
        full_path = malloc(path_len);
        if (!full_path) {
            perror("内存分配失败");
            free(path_copy);
            return NULL;
        }

        snprintf(full_path, path_len, "%s/%s", dir, command);

        // 检查文件是否存在且可执行
        struct stat st;
        if (stat(full_path, &st) == 0 && (st.st_mode & S_IXUSR)) {
            free(path_copy);
            return full_path; // 找到了，返回完整路径
        }

        free(full_path);
        full_path = NULL;
        dir = strtok(NULL, ":");
    }

    free(path_copy);
    return NULL; // 未找到
}

/**
 * 打印使用说明
 */
void print_usage(const char* program_name) {
    printf("使用方法: %s COMMAND [ARG]...\n", program_name);
    printf("启动COMMAND程序并统计其系统调用的占用时间\n");
    printf("\n");
    printf("示例:\n");
    printf("  %s ls /tmp\n", program_name);
    printf("  %s echo \"hello world\"\n", program_name);
}

int main(int argc, char *argv[]) {
    // 检查命令行参数
    if (argc < 2) {
        print_usage(argv[0]);
        return 1;
    }

    // 第一步：解析命令行参数并查找可执行文件
    const char* command = argv[1];
    printf("正在查找命令: %s\n", command);

    char* executable_path = find_executable_in_path(command);
    if (!executable_path) {
        fprintf(stderr, "错误: 找不到命令 '%s'\n", command);
        return 1;
    }

    printf("找到可执行文件: %s\n", executable_path);

    // 第二步：创建子进程并执行目标程序
    pid_t pid = fork();
    if (pid == -1) {
        perror("fork失败");
        free(executable_path);
        return 1;
    }

    if (pid == 0) {
        // 子进程：执行目标程序
        printf("子进程开始执行: %s\n", executable_path);

        // 准备execve的参数
        // argv[0]是sperf程序名，argv[1]是要执行的命令，argv[2]开始是命令的参数
        char** exec_argv = &argv[1]; // 从argv[1]开始就是要传给目标程序的参数

        // 调用execve执行目标程序
        execve(executable_path, exec_argv, environ);

        // 如果execve返回，说明执行失败
        perror("execve失败");
        exit(1);
    } else {
        // 父进程：等待子进程结束
        printf("父进程等待子进程(PID: %d)结束...\n", pid);

        int status;
        if (waitpid(pid, &status, 0) == -1) {
            perror("waitpid失败");
            free(executable_path);
            return 1;
        }

        if (WIFEXITED(status)) {
            printf("子进程正常结束，退出码: %d\n", WEXITSTATUS(status));
        } else if (WIFSIGNALED(status)) {
            printf("子进程被信号终止，信号: %d\n", WTERMSIG(status));
        }
    }

    free(executable_path);
    return 0;
}
