#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/wait.h>
#include <sys/stat.h>
#include <errno.h>
#include <fcntl.h>

// 全局变量：存储环境变量
extern char **environ;

/**
 * 在PATH环境变量中搜索可执行文件
 * @param command 要搜索的命令名
 * @return 找到的完整路径，需要调用者释放内存；如果未找到返回NULL
 */
char* find_executable_in_path(const char* command) {
    // 如果命令以'/'开头，说明是绝对路径，直接检查是否可执行
    if (command[0] == '/') {
        struct stat st;
        if (stat(command, &st) == 0 && (st.st_mode & S_IXUSR)) {
            return strdup(command);
        }
        return NULL;
    }

    // 获取PATH环境变量
    char* path_env = getenv("PATH");
    if (!path_env) {
        fprintf(stderr, "PATH环境变量未设置\n");
        return NULL;
    }

    // 复制PATH环境变量，因为strtok会修改原字符串
    char* path_copy = strdup(path_env);
    if (!path_copy) {
        perror("内存分配失败");
        return NULL;
    }

    char* dir = strtok(path_copy, ":");
    char* full_path = NULL;

    // 遍历PATH中的每个目录
    while (dir != NULL) {
        // 构造完整路径
        size_t path_len = strlen(dir) + strlen(command) + 2; // +2 for '/' and '\0'
        full_path = malloc(path_len);
        if (!full_path) {
            perror("内存分配失败");
            free(path_copy);
            return NULL;
        }

        snprintf(full_path, path_len, "%s/%s", dir, command);

        // 检查文件是否存在且可执行
        struct stat st;
        if (stat(full_path, &st) == 0 && (st.st_mode & S_IXUSR)) {
            free(path_copy);
            return full_path; // 找到了，返回完整路径
        }

        free(full_path);
        full_path = NULL;
        dir = strtok(NULL, ":");
    }

    free(path_copy);
    return NULL; // 未找到
}

/**
 * 查找strace可执行文件
 * @return strace的完整路径，需要调用者释放内存；如果未找到返回NULL
 */
char* find_strace() {
    // 尝试常见的strace路径
    const char* strace_paths[] = {
        "/usr/bin/strace",
        "/bin/strace",
        "/usr/local/bin/strace",
        NULL
    };

    for (int i = 0; strace_paths[i] != NULL; i++) {
        struct stat st;
        if (stat(strace_paths[i], &st) == 0 && (st.st_mode & S_IXUSR)) {
            return strdup(strace_paths[i]);
        }
    }

    // 如果在常见路径找不到，尝试在PATH中搜索
    return find_executable_in_path("strace");
}

/**
 * 打印使用说明
 */
void print_usage(const char* program_name) {
    printf("使用方法: %s COMMAND [ARG]...\n", program_name);
    printf("启动COMMAND程序并统计其系统调用的占用时间\n");
    printf("\n");
    printf("示例:\n");
    printf("  %s ls /tmp\n", program_name);
    printf("  %s echo \"hello world\"\n", program_name);
}

int main(int argc, char *argv[]) {
    // 检查命令行参数
    if (argc < 2) {
        print_usage(argv[0]);
        return 1;
    }

    // 第一步：解析命令行参数并查找可执行文件
    const char* command = argv[1];
    printf("正在查找命令: %s\n", command);

    char* executable_path = find_executable_in_path(command);
    if (!executable_path) {
        fprintf(stderr, "错误: 找不到命令 '%s'\n", command);
        return 1;
    }

    printf("找到可执行文件: %s\n", executable_path);

    // 第三步：查找strace
    char* strace_path = find_strace();
    if (!strace_path) {
        fprintf(stderr, "错误: 找不到strace程序\n");
        free(executable_path);
        return 1;
    }

    printf("找到strace: %s\n", strace_path);

    // 第四步：创建管道用于读取strace输出
    int pipefd[2];
    if (pipe(pipefd) == -1) {
        perror("创建管道失败");
        free(executable_path);
        free(strace_path);
        return 1;
    }

    printf("管道创建成功，读端: %d, 写端: %d\n", pipefd[0], pipefd[1]);

    // 创建子进程执行strace
    pid_t pid = fork();
    if (pid == -1) {
        perror("fork失败");
        close(pipefd[0]);
        close(pipefd[1]);
        free(executable_path);
        free(strace_path);
        return 1;
    }

    if (pid == 0) {
        // 子进程：执行strace追踪目标程序
        printf("子进程开始执行strace追踪: %s\n", executable_path);

        // 关闭管道读端
        close(pipefd[0]);

        // 将stderr重定向到管道写端（strace输出到stderr）
        if (dup2(pipefd[1], STDERR_FILENO) == -1) {
            perror("重定向stderr失败");
            exit(1);
        }
        close(pipefd[1]);

        // 构造strace的命令行参数
        // strace -T 目标程序完整路径 参数...
        int strace_argc = argc + 1; // strace + -T + 目标程序路径 + 原参数（从argv[2]开始）
        char** strace_argv = malloc(sizeof(char*) * (strace_argc + 1));
        if (!strace_argv) {
            perror("内存分配失败");
            exit(1);
        }

        strace_argv[0] = "strace";
        strace_argv[1] = "-T";  // 显示系统调用时间
        strace_argv[2] = executable_path;  // 使用找到的完整路径

        // 复制目标程序的参数（从argv[2]开始，即目标程序的参数）
        for (int i = 2; i < argc; i++) {
            strace_argv[i + 1] = argv[i];
        }
        strace_argv[strace_argc] = NULL;

        // 调试输出：显示构造的strace命令
        printf("构造的strace命令: ");
        for (int i = 0; i < strace_argc; i++) {
            printf("%s ", strace_argv[i]);
        }
        printf("\n");

        // 执行strace
        execve(strace_path, strace_argv, environ);

        // 如果execve返回，说明执行失败
        perror("execve strace失败");
        printf("strace路径: %s\n", strace_path);
        printf("目标程序路径: %s\n", executable_path);
        free(strace_argv);
        exit(1);
    } else {
        // 父进程：读取strace输出
        printf("父进程开始读取strace输出(子进程PID: %d)...\n", pid);

        // 关闭管道写端
        close(pipefd[1]);

        // 设置管道读端为非阻塞模式
        int flags = fcntl(pipefd[0], F_GETFL);
        fcntl(pipefd[0], F_SETFL, flags | O_NONBLOCK);

        char buffer[4096];
        ssize_t bytes_read;
        int line_count = 0;

        // 持续读取strace输出直到子进程结束
        while (1) {
            bytes_read = read(pipefd[0], buffer, sizeof(buffer) - 1);

            if (bytes_read > 0) {
                buffer[bytes_read] = '\0';
                printf("=== strace输出 ===\n%s", buffer);

                // 统计行数（简单演示）
                for (int i = 0; i < bytes_read; i++) {
                    if (buffer[i] == '\n') {
                        line_count++;
                    }
                }
            } else if (bytes_read == 0) {
                // 管道关闭，子进程可能已结束
                break;
            } else if (errno != EAGAIN && errno != EWOULDBLOCK) {
                perror("读取管道失败");
                break;
            }

            // 检查子进程是否还在运行
            int status;
            pid_t result = waitpid(pid, &status, WNOHANG);
            if (result == pid) {
                // 子进程已结束
                printf("子进程结束，最后读取剩余输出...\n");

                // 读取剩余数据
                while ((bytes_read = read(pipefd[0], buffer, sizeof(buffer) - 1)) > 0) {
                    buffer[bytes_read] = '\0';
                    printf("%s", buffer);
                    for (int i = 0; i < bytes_read; i++) {
                        if (buffer[i] == '\n') {
                            line_count++;
                        }
                    }
                }

                if (WIFEXITED(status)) {
                    printf("strace正常结束，退出码: %d\n", WEXITSTATUS(status));
                } else if (WIFSIGNALED(status)) {
                    printf("strace被信号终止，信号: %d\n", WTERMSIG(status));
                }
                break;
            } else if (result == -1 && errno != ECHILD) {
                perror("waitpid失败");
                break;
            }

            // 短暂休眠避免过度占用CPU
            usleep(10000); // 10ms
        }

        printf("总共读取了 %d 行strace输出\n", line_count);
        close(pipefd[0]);
    }

    free(executable_path);
    free(strace_path);
    return 0;
}
