#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/wait.h>
#include <sys/stat.h>
#include <errno.h>
#include <fcntl.h>
#include <sys/time.h>
#include <regex.h>

// 全局变量：存储环境变量
extern char **environ;

// 系统调用统计结构
typedef struct {
    char name[64];          // 系统调用名称
    double total_time;      // 总时间（秒）
    int count;              // 调用次数
} syscall_stat_t;

// 全局统计数据
#define MAX_SYSCALLS 256
syscall_stat_t syscall_stats[MAX_SYSCALLS];
int syscall_count = 0;
double total_execution_time = 0.0;

/**
 * 解析strace输出行，提取系统调用名称和时间
 * @param line strace输出的一行
 * @return 1表示成功解析，0表示解析失败
 */
int parse_strace_line(const char* line) {
    // strace输出格式示例：
    // write(1, "hello\n", 6) = 6 <0.000050>
    // openat(AT_FDCWD, "/etc/passwd", O_RDONLY) = 3 <0.000100>

    // 使用正则表达式匹配系统调用名称和时间
    regex_t regex;
    regmatch_t matches[3];

    // 正则表达式：匹配系统调用名称和时间
    // ^([a-zA-Z_][a-zA-Z0-9_]*)\(.*\) = .* <([0-9]+\.[0-9]+)>
    const char* pattern = "^([a-zA-Z_][a-zA-Z0-9_]*)\\(.*\\) = .* <([0-9]+\\.[0-9]+)>";

    if (regcomp(&regex, pattern, REG_EXTENDED) != 0) {
        return 0; // 正则表达式编译失败
    }

    if (regexec(&regex, line, 3, matches, 0) == 0) {
        // 提取系统调用名称
        int name_len = matches[1].rm_eo - matches[1].rm_so;
        char syscall_name[64];
        strncpy(syscall_name, line + matches[1].rm_so, name_len);
        syscall_name[name_len] = '\0';

        // 提取时间
        int time_len = matches[2].rm_eo - matches[2].rm_so;
        char time_str[32];
        strncpy(time_str, line + matches[2].rm_so, time_len);
        time_str[time_len] = '\0';
        double time_value = atof(time_str);

        // 查找或创建系统调用统计项
        int found = -1;
        for (int i = 0; i < syscall_count; i++) {
            if (strcmp(syscall_stats[i].name, syscall_name) == 0) {
                found = i;
                break;
            }
        }

        if (found == -1) {
            // 新的系统调用
            if (syscall_count < MAX_SYSCALLS) {
                strcpy(syscall_stats[syscall_count].name, syscall_name);
                syscall_stats[syscall_count].total_time = time_value;
                syscall_stats[syscall_count].count = 1;
                syscall_count++;
            }
        } else {
            // 已存在的系统调用，累加时间和次数
            syscall_stats[found].total_time += time_value;
            syscall_stats[found].count++;
        }

        total_execution_time += time_value;
        regfree(&regex);
        return 1;
    }

    regfree(&regex);
    return 0;
}

/**
 * 处理strace输出缓冲区，按行解析
 * @param buffer 包含strace输出的缓冲区
 * @param size 缓冲区大小
 */
void process_strace_output(const char* buffer, size_t size) {
    static char line_buffer[8192] = {0};
    static size_t line_pos = 0;

    for (size_t i = 0; i < size; i++) {
        if (buffer[i] == '\n') {
            // 完整的一行
            line_buffer[line_pos] = '\0';
            if (line_pos > 0) {
                // 解析这一行
                if (parse_strace_line(line_buffer)) {
                    printf("解析成功: %s\n", line_buffer);
                }
            }
            line_pos = 0;
        } else if (line_pos < sizeof(line_buffer) - 1) {
            line_buffer[line_pos++] = buffer[i];
        }
    }
}

/**
 * 输出系统调用统计信息（top 5）
 */
void print_syscall_stats() {
    if (syscall_count == 0 || total_execution_time == 0.0) {
        return;
    }

    // 按时间排序（冒泡排序，简单实现）
    for (int i = 0; i < syscall_count - 1; i++) {
        for (int j = 0; j < syscall_count - 1 - i; j++) {
            if (syscall_stats[j].total_time < syscall_stats[j + 1].total_time) {
                syscall_stat_t temp = syscall_stats[j];
                syscall_stats[j] = syscall_stats[j + 1];
                syscall_stats[j + 1] = temp;
            }
        }
    }

    // 输出top 5
    int top_count = syscall_count < 5 ? syscall_count : 5;
    for (int i = 0; i < top_count; i++) {
        int percentage = (int)((syscall_stats[i].total_time / total_execution_time) * 100);
        printf("%s (%d%%)\n", syscall_stats[i].name, percentage);
    }

    // 输出80个\0字符作为分隔
    for (int i = 0; i < 80; i++) {
        putchar('\0');
    }
    fflush(stdout);
}

/**
 * 在PATH环境变量中搜索可执行文件
 * @param command 要搜索的命令名
 * @return 找到的完整路径，需要调用者释放内存；如果未找到返回NULL
 */
char* find_executable_in_path(const char* command) {
    // 如果命令以'/'开头，说明是绝对路径，直接检查是否可执行
    if (command[0] == '/') {
        struct stat st;
        if (stat(command, &st) == 0 && (st.st_mode & S_IXUSR)) {
            return strdup(command);
        }
        return NULL;
    }

    // 获取PATH环境变量
    char* path_env = getenv("PATH");
    if (!path_env) {
        fprintf(stderr, "PATH环境变量未设置\n");
        return NULL;
    }

    // 复制PATH环境变量，因为strtok会修改原字符串
    char* path_copy = strdup(path_env);
    if (!path_copy) {
        perror("内存分配失败");
        return NULL;
    }

    char* dir = strtok(path_copy, ":");
    char* full_path = NULL;

    // 遍历PATH中的每个目录
    while (dir != NULL) {
        // 构造完整路径
        size_t path_len = strlen(dir) + strlen(command) + 2; // +2 for '/' and '\0'
        full_path = malloc(path_len);
        if (!full_path) {
            perror("内存分配失败");
            free(path_copy);
            return NULL;
        }

        snprintf(full_path, path_len, "%s/%s", dir, command);

        // 检查文件是否存在且可执行
        struct stat st;
        if (stat(full_path, &st) == 0 && (st.st_mode & S_IXUSR)) {
            free(path_copy);
            return full_path; // 找到了，返回完整路径
        }

        free(full_path);
        full_path = NULL;
        dir = strtok(NULL, ":");
    }

    free(path_copy);
    return NULL; // 未找到
}

/**
 * 查找strace可执行文件
 * @return strace的完整路径，需要调用者释放内存；如果未找到返回NULL
 */
char* find_strace() {
    // 尝试常见的strace路径
    const char* strace_paths[] = {
        "/usr/bin/strace",
        "/bin/strace",
        "/usr/local/bin/strace",
        NULL
    };

    for (int i = 0; strace_paths[i] != NULL; i++) {
        struct stat st;
        if (stat(strace_paths[i], &st) == 0 && (st.st_mode & S_IXUSR)) {
            return strdup(strace_paths[i]);
        }
    }

    // 如果在常见路径找不到，尝试在PATH中搜索
    return find_executable_in_path("strace");
}

/**
 * 打印使用说明
 */
void print_usage(const char* program_name) {
    printf("使用方法: %s COMMAND [ARG]...\n", program_name);
    printf("启动COMMAND程序并统计其系统调用的占用时间\n");
    printf("\n");
    printf("示例:\n");
    printf("  %s ls /tmp\n", program_name);
    printf("  %s echo \"hello world\"\n", program_name);
}

int main(int argc, char *argv[]) {
    // 检查命令行参数
    if (argc < 2) {
        print_usage(argv[0]);
        return 1;
    }

    // 第一步：解析命令行参数并查找可执行文件
    const char* command = argv[1];
    printf("正在查找命令: %s\n", command);

    char* executable_path = find_executable_in_path(command);
    if (!executable_path) {
        fprintf(stderr, "错误: 找不到命令 '%s'\n", command);
        return 1;
    }

    printf("找到可执行文件: %s\n", executable_path);

    // 第三步：查找strace
    char* strace_path = find_strace();
    if (!strace_path) {
        fprintf(stderr, "错误: 找不到strace程序\n");
        free(executable_path);
        return 1;
    }

    printf("找到strace: %s\n", strace_path);

    // 第四步：创建管道用于读取strace输出
    int pipefd[2];
    if (pipe(pipefd) == -1) {
        perror("创建管道失败");
        free(executable_path);
        free(strace_path);
        return 1;
    }

    printf("管道创建成功，读端: %d, 写端: %d\n", pipefd[0], pipefd[1]);

    // 创建子进程执行strace
    pid_t pid = fork();
    if (pid == -1) {
        perror("fork失败");
        close(pipefd[0]);
        close(pipefd[1]);
        free(executable_path);
        free(strace_path);
        return 1;
    }

    if (pid == 0) {
        // 子进程：执行strace追踪目标程序
        printf("子进程开始执行strace追踪: %s\n", executable_path);

        // 关闭管道读端
        close(pipefd[0]);

        // 将stderr重定向到管道写端（strace输出到stderr）
        if (dup2(pipefd[1], STDERR_FILENO) == -1) {
            perror("重定向stderr失败");
            exit(1);
        }
        close(pipefd[1]);

        // 构造strace的命令行参数
        // strace -T 目标程序完整路径 参数...
        int strace_argc = argc + 1; // strace + -T + 目标程序路径 + 原参数（从argv[2]开始）
        char** strace_argv = malloc(sizeof(char*) * (strace_argc + 1));
        if (!strace_argv) {
            perror("内存分配失败");
            exit(1);
        }

        strace_argv[0] = "strace";
        strace_argv[1] = "-T";  // 显示系统调用时间
        strace_argv[2] = executable_path;  // 使用找到的完整路径

        // 复制目标程序的参数（从argv[2]开始，即目标程序的参数）
        for (int i = 2; i < argc; i++) {
            strace_argv[i + 1] = argv[i];
        }
        strace_argv[strace_argc] = NULL;

        // 调试输出：显示构造的strace命令
        printf("构造的strace命令: ");
        for (int i = 0; i < strace_argc; i++) {
            printf("%s ", strace_argv[i]);
        }
        printf("\n");

        // 执行strace
        execve(strace_path, strace_argv, environ);

        // 如果execve返回，说明执行失败
        perror("execve strace失败");
        printf("strace路径: %s\n", strace_path);
        printf("目标程序路径: %s\n", executable_path);
        free(strace_argv);
        exit(1);
    } else {
        // 父进程：读取strace输出并进行解析
        printf("父进程开始读取strace输出(子进程PID: %d)...\n", pid);

        // 关闭管道写端
        close(pipefd[1]);

        // 设置管道读端为非阻塞模式
        int flags = fcntl(pipefd[0], F_GETFL);
        fcntl(pipefd[0], F_SETFL, flags | O_NONBLOCK);

        char buffer[4096];
        ssize_t bytes_read;
        int line_count = 0;

        // 定时输出相关变量
        struct timeval last_output_time, current_time;
        gettimeofday(&last_output_time, NULL);
        const long output_interval_us = 100000; // 100ms = 100,000微秒

        // 持续读取strace输出直到子进程结束
        while (1) {
            bytes_read = read(pipefd[0], buffer, sizeof(buffer) - 1);

            if (bytes_read > 0) {
                buffer[bytes_read] = '\0';

                // 处理strace输出，进行解析
                process_strace_output(buffer, bytes_read);

                // 统计行数
                for (int i = 0; i < bytes_read; i++) {
                    if (buffer[i] == '\n') {
                        line_count++;
                    }
                }

                // 检查是否需要输出统计信息（每100ms一次）
                gettimeofday(&current_time, NULL);
                long time_diff = (current_time.tv_sec - last_output_time.tv_sec) * 1000000 +
                                (current_time.tv_usec - last_output_time.tv_usec);

                if (time_diff >= output_interval_us && syscall_count > 0) {
                    printf("\n=== 系统调用统计 (%.1fs) ===\n",
                           current_time.tv_sec - last_output_time.tv_sec +
                           (current_time.tv_usec - last_output_time.tv_usec) / 1000000.0);
                    print_syscall_stats();
                    last_output_time = current_time;
                }

            } else if (bytes_read == 0) {
                // 管道关闭，子进程可能已结束
                break;
            } else if (errno != EAGAIN && errno != EWOULDBLOCK) {
                perror("读取管道失败");
                break;
            }

            // 检查子进程是否还在运行
            int status;
            pid_t result = waitpid(pid, &status, WNOHANG);
            if (result == pid) {
                // 子进程已结束
                printf("子进程结束，最后读取剩余输出...\n");

                // 读取剩余数据
                while ((bytes_read = read(pipefd[0], buffer, sizeof(buffer) - 1)) > 0) {
                    buffer[bytes_read] = '\0';
                    process_strace_output(buffer, bytes_read);
                    for (int i = 0; i < bytes_read; i++) {
                        if (buffer[i] == '\n') {
                            line_count++;
                        }
                    }
                }

                // 输出最终统计结果
                if (syscall_count > 0) {
                    printf("\n=== 最终系统调用统计 ===\n");
                    print_syscall_stats();
                }

                if (WIFEXITED(status)) {
                    printf("strace正常结束，退出码: %d\n", WEXITSTATUS(status));
                } else if (WIFSIGNALED(status)) {
                    printf("strace被信号终止，信号: %d\n", WTERMSIG(status));
                }
                break;
            } else if (result == -1 && errno != ECHILD) {
                perror("waitpid失败");
                break;
            }

            // 短暂休眠避免过度占用CPU
            usleep(10000); // 10ms
        }

        printf("总共读取了 %d 行strace输出\n", line_count);
        printf("解析了 %d 个不同的系统调用\n", syscall_count);
        printf("总执行时间: %.6f 秒\n", total_execution_time);
        close(pipefd[0]);
    }

    free(executable_path);
    free(strace_path);
    return 0;
}
